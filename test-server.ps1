# PowerShell script to test DNS server
Write-Host "=== Testing DNS Server Stage 5 ===" -ForegroundColor Green

try {
    # Create UDP client
    $udpClient = New-Object System.Net.Sockets.UdpClient
    $serverEndpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Loopback, 2053)
    
    # Test 1: Standard query with packet ID 0x1234
    Write-Host "Test 1: Standard Query (ID: 0x1234, OpCode: 0)" -ForegroundColor Yellow
    $query1 = @(0x12, 0x34, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00)
    $udpClient.Send($query1, $query1.Length, $serverEndpoint) | Out-Null
    
    $result1 = $udpClient.Receive([ref]$serverEndpoint)
    $responseId1 = ($result1[0] -shl 8) -bor $result1[1]
    $flags1 = ($result1[2] -shl 8) -bor $result1[3]
    $qr1 = ($result1[2] -band 0x80) -ne 0  # QR is bit 7 of first flags byte
    $rcode1 = $result1[3] -band 0x0F       # RCODE is lower 4 bits of second flags byte

    Write-Host "  Response: ID=0x$($responseId1.ToString('X4')), QR=$qr1, RCODE=$rcode1" -ForegroundColor Cyan
    Write-Host "  Raw bytes: $($result1[0..3] | ForEach-Object { '0x{0:X2}' -f $_ })" -ForegroundColor Gray

    if ($responseId1 -eq 0x1234 -and $qr1 -and $rcode1 -eq 0) {
        Write-Host "  ✓ Test 1 PASSED" -ForegroundColor Green
    } else {
        Write-Host "  ✗ Test 1 FAILED" -ForegroundColor Red
        exit 1
    }
    
    # Test 2: Non-standard query with packet ID 0x5678, OpCode 1
    Write-Host "Test 2: Non-standard Query (ID: 0x5678, OpCode: 1)" -ForegroundColor Yellow
    $query2 = @(0x56, 0x78, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00)
    $udpClient.Send($query2, $query2.Length, $serverEndpoint) | Out-Null
    
    $result2 = $udpClient.Receive([ref]$serverEndpoint)
    $responseId2 = ($result2[0] -shl 8) -bor $result2[1]
    $flags2 = ($result2[2] -shl 8) -bor $result2[3]
    $qr2 = ($result2[2] -band 0x80) -ne 0  # QR is bit 7 of first flags byte
    $rcode2 = $result2[3] -band 0x0F       # RCODE is lower 4 bits of second flags byte
    
    Write-Host "  Response: ID=0x$($responseId2.ToString('X4')), QR=$qr2, RCODE=$rcode2" -ForegroundColor Cyan
    
    if ($responseId2 -eq 0x5678 -and $qr2 -and $rcode2 -eq 4) {
        Write-Host "  ✓ Test 2 PASSED" -ForegroundColor Green
    } else {
        Write-Host "  ✗ Test 2 FAILED" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "🎉 ALL INTEGRATION TESTS PASSED!" -ForegroundColor Green
    Write-Host "Stage 5 DNS server is working correctly with real network communication." -ForegroundColor Green
    
} catch {
    Write-Host "❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    if ($udpClient) {
        $udpClient.Close()
    }
}
